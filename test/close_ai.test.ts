import { DataService } from '../bot/service/moer/getter/getData'
import { ChatDB } from '../bot/service/moer/database/chat'
import { StringOutputParser } from '@langchain/core/output_parsers'
import { CheapOpenAI } from '../bot/lib/ai/llm/client'
import { ChatPromptTemplate, PromptTemplate } from '@langchain/core/prompts'
import { LLM } from '../bot/lib/ai/llm/LLM'
import { HumanMessage } from '@langchain/core/messages'
import { MoerAPI } from '../bot/model/moer_api/moer'
import { PrismaMongoClient } from '../bot/model/mongodb/prisma'

describe('Test', function () {
  beforeAll(() => {

  })
  it('contact', async () => {
    const fk = `🙆🏻‍♀️您目前的生活角色？
1-职场奋斗者
2-家庭管理者
3-退休精进者
4-修行者

🐣您的冥想经验值？
5-纯小白
6-接触过
7-有基础

🎯最想点亮的人生议题
8-情绪减压
9-专注提升
10-睡眠改善
11-财富能量
12-亲密关系
13-灵性成长

——————
回复班班数字就好，例如：1-5-8，班班统计好，课前给老师参考`
    console.log(/生活角色|冥想经验值|人生议题|回复.*数字/.test(fk))

    // console.log(JSON.stringify(await JuziAPI.getCustomerInfo('1688857949631398', '7881301910030394'), null, 4))
    // console.log(JSON.stringify(await MoerAPI.getUserByPhone('17677433157'), null, 4))

    // await ChatDB.create({
    //   id: '7881301910030394_1688857949631398',
    //   round_ids: [],
    //   wx_id: '1688857949631398', // 如果 Chat 模型有 wx_id 字段
    //   created_at: new Date(),
    //   chat_state: ChatStateStore.get('7881301910030394_1688857949631398'),
    //   contact: {
    //     wx_id: '7881301910030394',
    //     wx_name: '@微信'
    //   }
    // })
  }, 60000)

  it('类型测试', async () => {
    const client = new CheapOpenAI()
    const parser = new StringOutputParser()
    const template = PromptTemplate.fromTemplate('请用 {tool} 解答以下问题：{question}')

    // await template.pipe(client).pipe(parser).invoke({ tool: 'ChatGPT', question: '你好' })

    console.log(await LLM.predict(template, undefined, { tool: 'ChatGPT', question: '你好' }))
    console.log(await LLM.predict('你好'))
  }, 60000)

  it('类型测试2', async () => {
    const client = new CheapOpenAI()
    const promptTemplate = ChatPromptTemplate.fromMessages<{chat_history: string}>([
      ['system', 'hello'],
      ['user', '{chat_history}']
    ])
    const parser = new StringOutputParser()

    // await promptTemplate.pipe(client).pipe(parser).invoke({ chat_history: 'hi' })

    console.log(await new LLM().predictMessage(promptTemplate, { chat_history: 'hi' }))
    console.log(await new LLM().predictMessage([new HumanMessage('hi')]))
  }, 60000)

  it('should pass', async () => {
    const chats = await DataService.getChatsByCourseNo(51)
    await Promise.all(
      chats.map(async (chat) => {
        if (chat.wx_id === '1688857003605938') {
          await ChatDB.setStopGroupPush(chat.id, false)
          await ChatDB.setHumanInvolvement(chat.id, false)
        }
      })
    )
  }, 60000)

  it('delete 53', async () => {
    const chats = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        course_no: 53,
        // NOT: {
        //   is_test: true
        // }
      }
    })

    for (const chat of chats) {
      if (chat.contact.wx_name.includes('250523-SYQ-MacBook-Pro')) {
        await ChatDB.removeById(chat.id)
      }
    }
  }, 60000)
})