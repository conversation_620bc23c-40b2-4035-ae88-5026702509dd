import { loadConfigByWxId } from '../../../../../../test/tools/load_config'
import { getBotId, getUserId } from '../../../../../config/chat_id'
import { Config } from '../../../../../config/config'
import { ChatStatStoreManager } from '../../../storage/chat_state_store'
import { MoerVisualizedSopProcessor } from '../moer_visualized_sop_processor'
import { sendMaterial, sendMaterialById, VisualizedSopProcessor } from '../visualized_sop_processor'
import { VisualizedSopTasks } from '../visualized_sop_task_starter'

describe('测试sop', () => {

  const chatId = '7881302146051227_1688858254705213'
  const userId = getUserId(chatId)
  const botId = getBotId(chatId)
  test('测试清空sop', async() => {
    Config.setting.wechatConfig = await loadConfigByWxId('1688857404698934')
    await VisualizedSopTasks.clearSop('7881302146051227_1688857404698934')
  }, 6000000)

  test('测试sop', async() => {
    Config.setting.wechatConfig = await loadConfigByWxId(botId)
    await ChatStatStoreManager.initState(chatId)
    await new MoerVisualizedSopProcessor().handleSopBySopId(chatId, userId, '685113ec85ac51120f6990a8')
  }, 60000000)

  it('测试 SOP1', async () => {
    Config.setting.wechatConfig = await loadConfigByWxId('1688857003605938')

    const task = {
      name: '6854bc995a9273b53de85009',
      chatId: '7881302234048314_1688857003605938',
      userId: '7881302234048314',
      scheduleTime: { day: 1, week: 0, time: '21:43:00', timeAnchor: 'course' },
      sendTime: '2025-07-28T13:43:00.000Z'
    }


    await ChatStatStoreManager.initState(task.chatId)
    await new MoerVisualizedSopProcessor().handleSopBySopId(task.chatId, task.userId, task.name)
  }, 30000)

  it('测试发送material', async() => {
    Config.setting.localTest = false
    Config.setting.wechatConfig = await loadConfigByWxId(botId)
    await sendMaterialById(userId, chatId, '68bf9cf8adbf9507836c3ff8', true)
  }, 30000)
})