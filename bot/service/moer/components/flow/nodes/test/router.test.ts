import { Router } from '../router'
import { DataService } from '../../../../getter/getData'
import { isScheduleTimeAfter } from '../../../schedule/creat_schedule_task'
import { getUserId } from '../../../../../../config/chat_id'
import { LLM } from '../../../../../../lib/ai/llm/LLM'
import { LLMXMLHelper } from '../../helper/xmlHelper'
import { SystemMessagePromptTemplate } from '@langchain/core/prompts'


describe('NodeRouter', () => {

  it('currentTime', async () => {
    const currentTime = await DataService.getCurrentTime('7881303470906700_1688856322643146')

    console.log(isScheduleTimeAfter(currentTime, {
      day: 1,
      time: '20:00:00',
      is_course_week: true
    }))

  }, 60000)


  it('test Survey', async () => {
    const testCases = [
      // '129',
      // '三天还是五天',
      // '我选择1-5-8',
      // '1-6-8',
      // '3—6—10',
      // '4-5-8910111213',
      // '1，6,13',
      // '1-5-11',
      // '1-7-8',
      // '一-五-八',
      // '1 6 8',
      // '🙆🏻‍♀️.您目前的生活角色？\n1-职场奋斗者\n\n🐣.您的冥想经验值？\n 5-纯小白\n  \n🎯.最想点亮的人生议题\n   8- 情绪减压\n   9-  专注提升',
      // // 反例 - 应该返回false的情况
      // '',                  // 空字符串
      // 'hello world',       // 没有数字
      // '1-2-3',             // 3不是有效的议题选项
      // '1-5',               // 缺少议题
      // '8-9-10',            // 缺少角色和经验
      // '4-8',               // 缺少经验
      // '6-13',              // 缺少角色
      // '0-5-8',             // 0不是有效角色
      // '1-0-8',             // 0不是有效经验
      // '1-5-0',              // 0不是有效议题
      // '159',
      // '1',
      // '1513',
      // '461013',
      // '358',
      '141516',
      '24589 10 11 13'
    ]

    for (const testCase of testCases) {
      const isRepliedSurvey = await Router.checkIsRepliedSurvey('', testCase)
      console.log('isRepliedSurvey: ', isRepliedSurvey)
    }
  }, 60000)

  it('test robot detection', async () => {
    const chat_id = '7881302298050442_1688858254705213'
    const user_id = getUserId(chat_id)
    const userMessage = '是的，最近一直在做AI'
    const isRobotDetection = await Router.checkRobotDetection(chat_id, '', user_id, userMessage)
    console.log('isRobotDetection', isRobotDetection)
  }, 60000)


  it('test small lecture completion detection', async () => {
    const testCases: Array<{ chat: string; expected: boolean }> = [
      { chat: '麦子老师: 建议您先花10分钟看下小讲堂\n客户: 已经看完了', expected: true },
      { chat: '麦子老师: 小讲堂看了吗？\n客户: 看过了', expected: true },
      { chat: '麦子老师: 小讲堂听了没？\n客户: 小讲堂听过了', expected: true },
      { chat: '麦子老师: 记得看小讲堂哦\n客户: 小讲堂我听过很多次', expected: true },
      { chat: '麦子老师: 小讲堂重要哦\n客户: 昨晚就学完啦', expected: true },
      { chat: '麦子老师: 小讲堂看了吗？\n客户: 还没呢', expected: false },
      { chat: '麦子老师: 小讲堂看了吗？\n客户: 等会去看', expected: false },
      { chat: '麦子老师: 建议看小讲堂\n客户: 好的老师', expected: false },
      { chat: '麦子老师: 你最近看过《泰坦尼克号》么\n客户: 看过了', expected: false },
      { chat: '麦子老师: 同学你完课礼领了么？\n客户: 领完了', expected: false },
    ]

    const prompt = SystemMessagePromptTemplate.fromTemplate(`You are tasked with analyzing a conversation to determine if a user has indicated they have completed or watched a small lecture (called "小讲堂" in Chinese). This is an important task to track student progress.
    
I will provide you with a conversation between a teacher and a student. Your job is to analyze whether the student's response indicates they have already watched or completed the small lecture.

When determining this, look for phrases that clearly indicate completion such as:
- "看完了" (watched/finished)
- "学完了" (learned/completed)
- "看过了" (have watched)
- "学过了" (have learned)
- "听过了" (have listened)
- Phrases that directly state they've already completed it
- Similar expressions that confirm completion

First, carefully analyze the conversation in <thinking> tags, identifying key phrases that indicate completion status. Then return your conclusion as either true (they have completed it) or false (they have not completed it) inside <result> tags.

Important: Only determine true if the user specifically indicates they've completed the small lecture (小讲堂). If they say they've watched/completed something else, or if it's ambiguous what they've completed, return false.

Here are some examples:

Example 1:
"麦子老师: 建议您先花10分钟看下唐宁老师的小讲堂，里面有详细介绍后续课程安排，还能提前体验一次「海浪冥想」，对后面学习特别重要哦～记得看完告诉我一声，我这边送您电子版《冥想练习指南》当小奖励哈～
客户: 已经学过了哦"

<thinking>
In this conversation, the teacher recommends that the user spend 10 minutes watching Teacher Tang Ning's small lecture (小讲堂). The user responds with "已经学过了哦" which means "I have already learned/studied it." This clearly indicates the user has completed the small lecture.
</thinking>
<result>
true
</result>

Example 2:
"麦子老师: 哈哈哈催了同学好几次了，我都不好意思了，你的小讲堂还没听哦~全班就剩下你了。:https:/t.meihao.com/KKZd
客户: 我听几次了"

<thinking>
The teacher is reminding the user that they haven't listened to the small lecture yet (小讲堂还没听). However, the user responds with "我听几次了" meaning "I've listened to it several times." This clearly contradicts the teacher's statement and indicates the user has completed the small lecture multiple times.
</thinking>
<result>
true
</result>

Example 3:
"麦子老师: 你最近看过《泰坦尼克号》么
客户: 这个我看过了"

<thinking>
In this conversation, the teacher is asking if the user has watched the movie "Titanic" recently. The user responds with "这个我看过了" which means "I have watched this." However, this is about watching the movie Titanic, not about completing the small lecture (小讲堂). Therefore, the user has not indicated completion of the small lecture.
</thinking>
<result>
false
</result>

Now, analyze the following conversation:

<conversation>
{chat_history}
</conversation>`)

    for (const { chat, expected } of testCases) {
      const llmRes = await LLM.predict(prompt, {}, { chat_history: chat })
      const result = await LLMXMLHelper.extractBooleanAnswer(
        llmRes,
        { tagName: 'result', trueFlag: 'true', falseFlag: 'false' },
      )
      expect(result).toBe(expected)
    }
  }, 120000)
})