import logger from '../../../../model/logger/logger'
import { MoerWorkFlowNode, trackInvoke } from '../flow/nodes/baseNode'
import { IWorkflowState } from '../flow/flow'
import { MoerNode } from '../flow/nodes/type'
import { LLM } from '../../../../lib/ai/llm/LLM'
import { LLMNode } from '../flow/nodes/llm'
import { ChatStateStore } from '../../storage/chat_state_store'
import { FreeThink } from './freethink'
import { PostSaleNode } from '../flow/nodes/postSaleNode'
import { MetaActionRouter } from '../meta_action/meta_action_router'
import { getPrompt } from './prompt'
import { SalesNodeHelper } from '../flow/helper/salesNodeHelper'

export class FreeTalk extends MoerWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const chat_id = state.chat_id
    if (ChatStateStore.get(chat_id).state.is_complete_payment && !ChatStateStore.get(chat_id).state.is_complete_post_sale) {
      return await PostSaleNode.invoke(state)
    }
    // 针对外部测试人员的降智版本
    const test_chat_ids = ['7881299599245568_1688855548631328']
    let action: string[]
    let strategy: string
    let metaActionStageInfo = ''

    if (test_chat_ids.includes(chat_id)) {
      action = ['']
      strategy = '跟客户开玩笑，完全无下限'
    } else {
      const metaActionStage = await MetaActionRouter.getThinkAndMetaActions(chat_id, state.round_id)
      const result = await FreeThink.invoke(state, metaActionStage)
      metaActionStageInfo = await MetaActionRouter.getGuidance(chat_id, result.strategy, result.action, state.round_id)
      action = result.action
      strategy = result.strategy
    }
    const actionInfo = await MetaActionRouter.handleAction(chat_id, state.round_id, action)
    const talkStrategyPrompt = [
      '无论客户最后说什么，参考上述信息回答完客户后，都要严格执行下面的策略，要求极度精简，点到为止',
      strategy,
      metaActionStageInfo,
      actionInfo.guidance
    ].filter(Boolean).join('\n')

    // build the context
    const composerPrompt = await getPrompt('free-compose')
    const dialogHistory = await SalesNodeHelper.getChatHistory(state.chat_id, 3, 9)
    const output = await LLM.predict(
      composerPrompt, {
        model: 'gpt-5-mini',
        responseJSON: true,
        meta: {
          promptName: 'composer',
          chat_id: state.chat_id,
          round_id: state.round_id,
        } }, {
        dialogHistory: dialogHistory,
        talkStrategy: strategy,
      })

    let module: string[] = []
    try {
      const parsedOutput = JSON.parse(output)
      module = parsedOutput.module
    } catch (error) {
      logger.error('Composer 解析 JSON 失败:', error)
    }

    await LLMNode.invoke({
      state,
      promptName: 'free_talk',
      model: test_chat_ids.includes(chat_id) ? 'gpt-5-mini' : 'gpt-5-chat',
      courseConfig: module.includes('课程设置'),
      retrievedKnowledge: module.includes('补充知识'),
      customerMemory: module.includes('客户记忆'),
      customerBehavior: module.includes('客户行为'),
      customerPortrait: module.includes('客户画像'),
      temporalInformation: module.includes('时间信息'),
      talkStrategyPrompt: talkStrategyPrompt,
      noStagePrompt: true,
      postReplyCallBack: actionInfo.callback
    })
    return MoerNode.FreeTalk
  }
}