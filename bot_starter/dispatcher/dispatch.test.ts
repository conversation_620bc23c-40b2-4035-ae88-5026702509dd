import { FileHelper } from '../../bot/lib/file'
import path from 'path'
import { JSONHelper } from '../../bot/lib/json/json'
import { DataService } from '../../bot/service/moer/getter/getData'
import { ClientAccountConfig } from '../config/account'
import { Queue } from 'bullmq'
import { RedisDB } from '../../bot/model/redis/redis'
import { ClassGroupTaskManager } from '../client/class_group'
import { Config } from '../../bot/config/config'
import { loadConfigByAccountName } from '../../test/tools/load_config'
import { CacheDecorator } from '../../bot/lib/cache/cache'
import { ChatDB } from '../../bot/service/moer/database/chat'
import { PrismaMongoClient } from '../../bot/model/mongodb/prisma'
import { Types } from 'mongoose'


describe('Test', function () {
  beforeAll(() => {

  })
  it('********312', async () => {
    console.log(new Date(*************).toLocaleString())
  }, 60000)

  it('12asdasd', async () => {
    // console.log(await DataService.getCourseLink(2, '7881301413991996_1688858047620029'))
    Config.setting.wechatConfig = await loadConfigByAccountName('moer3')

    console.log(Boolean(Config.setting.wechatConfig?.isGroupOwner))
  }, 60000)

  it('更新群配置', async () => {
    // 补一下 moer 21 群发任务 （更新下群信息）
    await PrismaMongoClient.getConfigInstance().config.update({
      where: {
        id: '68b19c93c1e8675d454e0116'
      },
      data: {
        enterpriseConfig: {
          'notifyGroupId': 'R:*****************',
          'isGroupOwner': true,
          'classGroupIds': [
            'R:*****************',
            'R:*****************',
            'R:*****************',
            'R:*****************'
          ]
        }
      }
    })

  }, 30000)

  it('补加群发任务', async () => {
    Config.setting.wechatConfig = await loadConfigByAccountName('moer21')
    // DataService.getCourseStartTimeByCourseNo = CacheDecorator.decorateAsync(DataService.getCourseStartTimeByCourseNo)
    //
    // await ClassGroupTaskManager.createTasks()

    const queue = new Queue(`send_${Config.setting.wechatConfig?.id}_class_group_tasks`, {    connection: RedisDB.getInstance() })

    console.log(JSON.stringify(await queue.getJobs(), null, 4))
  }, 60000)

  it('获取一下期数', async () => {
    const accounts = ['qiaoqiao',  'moer5', 'moer10', 'moer12', 'moer20']

    for (const account of accounts) {
      Config.setting.wechatConfig = await loadConfigByAccountName(account)
      const chat = await ChatDB.getById(`${Config.setting.wechatConfig?.id}_${Config.setting.wechatConfig?.classGroupId.replaceAll(':', '')}`)
      if (chat) {
        console.log(chat.course_no, account, chat.id)
      }
    }

  }, 30000)

  it('********', async () => {
    const wx_id = await DataService.getWxIdByMoerId('941184')
    if (!wx_id) {
      return
    }

    const serverAddress = ClientAccountConfig.getServerAddressByWechatId(wx_id)

    if (!serverAddress) {
      return null
    }

    console.log(serverAddress)
  }, 60000)

  it('should pass', async () => {
    // 读取配置文件，构建 map，选择对应的服务地址
    const serverFile = await FileHelper.readFile(path.join(__dirname, 'server.json'))
    const serverMap = JSONHelper.parse(serverFile)

    console.log(serverMap['abc'])
  })
})